const HttpMethod = Object.freeze({
  get: 'GET',
  post: 'POST',
  put: 'PUT',
  delete: 'DELETE',
  patch: 'PATCH',
  options: 'OPTIONS',
  head: 'HEAD',
  trace: 'TRACE',
  connect: 'CONNECT',
})

const AuthMessage = Object.freeze({
  MISSING_TOKEN: 'Missing authorization token',
  TOKEN_EXPIRED: 'Token has been expired',
  NO_PERMISSING: 'User does not have permission',
  COMMON_AUTH_FAILED: 'Login fail',
  SUCCESS: 'success',
  SESSION_EXPIRED: 'Session expired',
})
const PackageType = Object.freeze({
  USER: 'user',
  DEPARTMENT: 'department',
})
const RecordStatus = Object.freeze({
  EDITABLE: 'editable',
  FINALIZED: 'finalized',
})
const LabTestStatus = Object.freeze({
  NOT_PAID: 'Not Paid',
  AWAITED: 'Awaited',
  READY: 'Ready',
  UPLOAD: 'Upload',
  UPLOADED: 'Uploaded',
  PAID: 'Paid',
})

const AppointmentStatus = Object.freeze({
  AWAITING: 'Awaiting',
  BOOKED: 'Booked',
  PRIORITY: 'Priority',
  INVESTIGATIONS: 'Investigations',
  CONSULTATION: 'Consultation',
  DONE: 'Done',
  CANCELLED: 'Cancelled',
})

const PatientQueueStatus = Object.freeze({
  BOOKED: 'Booked',
  ARRIVED: 'Arrived',
  PROXY_VISIT: 'ProxyVisit',
  NO_SHOW: 'NoShow',
})

const PatientQueueLabStatus = Object.freeze({
  REGISTERED_NOT_PAID: 'Registered/Not paid',
  LAB_REPORT_AWAITING: 'Lab reports awaiting',
  LAB_REPORT_READY: 'Lab reports ready',
  NO_TEST_ORDERED: 'No tests ordered',
})

const PaymentStatus = Object.freeze({
  CREATED: 'created',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
})

const PaymentType = Object.freeze({
  PATIENT_REGISTRATION: 'patient_registration',
  CONSULTATION: 'consultation',
  PRESCRIPTION: 'prescription',
  LAB_TEST: 'lab_test',
})

const DiagnosisStatus = Object.freeze({
  PROVISIONAL: 'provisional',
  CONFIRMED: 'confirmed',
})

const ActivityStatus = Object.freeze({
  ACTIVE: 'active',
  INACTIVE: 'inactive',
})

const PrescriptionStatus = Object.freeze({
  PAID: 'paid',
  UNPAID: 'unpaid',
})

const DashboardFilter = Object.freeze({
  TODAY: 'today',
  YESTERDAY: 'yesterday',
  LAST_7_DAYS: 'last_7_days',
  LAST_15_DAYS: 'last_15_days',
  LAST_30_DAYS: 'last_30_days',
  LAST_MONTH: 'last_month',
  LAST_90_DAYS: 'last_90_days',
  CUSTOM: 'custom',
  ALL: 'all',
})

const NutritionMetric = Object.freeze({
  CALORIES: 'calories',
  CARBS: 'carbs',
  PROTEIN: 'protein',
  FAT: 'fat',
  SUGAR: 'sugar',
  FIBER: 'fiber',
  CALCIUM: 'calcium',
  MAGNESIUM: 'magnesium',
  PHOSPHORUS: 'phosphorus',
  IRON: 'iron',
  SODIUM: 'sodium',
  POTASSIUM: 'potassium',
  SALT: 'salt',
  OIL: 'oil',
  SFA: 'sfa',
  MUFA: 'mufa',
  PUFA: 'pufa',
  CHOLESTEROL: 'cholesterol',
})

const MacroMetric = Object.freeze({
  CALORIES: 'calories',
  CARBS: 'carbs',
  PROTEIN: 'protein',
  FAT: 'fat',
  SUGAR: 'sugar',
  SALT: 'salt',
  SFA: 'sfa',
  MUFA: 'mufa',
  PUFA: 'pufa',
  FIBER: 'fiber',
  CHOLESTEROL: 'cholesterol',
})

const MicroMetric = Object.freeze({
  CALCIUM: 'calcium',
  MAGNESIUM: 'magnesium',
  PHOSPHORUS: 'phosphorus',
  IRON: 'iron',
  SODIUM: 'sodium',
  POTASSIUM: 'potassium',
})

const AdditionalMetric = Object.freeze({
  SALT: 'salt',
  SUGAR: 'sugar',
})
const MacroChartMetric = Object.freeze({
  CALORIES: 'calories',
  CARBS: 'carbs',
  PROTEIN: 'protein',
  FAT: 'fat',
  FIBER: 'fiber',
})

const MealType = Object.freeze({
  BREAKFAST: 'Breakfast',
  LUNCH: 'Lunch',
  DINNER: 'Dinner',
  MID_MORNING: 'Mid morning',
  EVENING_SNACK: 'Evening snack',
  POST_DINNER_SNACK: 'Post dinner snack',
})

const MealCategory = Object.freeze({
  MEALS: [MealType.BREAKFAST, MealType.LUNCH, MealType.DINNER],
  SNACKS: [
    MealType.MID_MORNING,
    MealType.EVENING_SNACK,
    MealType.POST_DINNER_SNACK,
  ],
})

module.exports = {
  HttpMethod,
  AuthMessage,
  PackageType,
  RecordStatus,
  LabTestStatus,
  AppointmentStatus,
  PatientQueueStatus,
  PatientQueueLabStatus,
  PaymentStatus,
  PaymentType,
  DiagnosisStatus,
  ActivityStatus,
  PrescriptionStatus,
  DashboardFilter,
  NutritionMetric,
  MicroMetric,
  AdditionalMetric,
  MacroMetric,
  MacroChartMetric,
  MealType,
  MealCategory,
}
