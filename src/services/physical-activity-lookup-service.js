const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')

const physicalActivityContainer = 'PhysicalActivity'

class PhysicalActivityLookupService {
  constructor() {
    this.activityCache = null
    this.lastCacheUpdate = null
    this.cacheExpiryMinutes = 60 // Cache for 1 hour
  }

  // Get all activities from PhysicalActivity container and cache them
  async loadActivitiesFromDatabase() {
    try {
      const query = 'SELECT * FROM c'
      const activities = await cosmosDbContext.queryItems(
        query,
        physicalActivityContainer,
      )

      this.activityCache = activities || []
      this.lastCacheUpdate = new Date()

      logging.logInfo(
        `Loaded ${this.activityCache.length} activities from database`,
      )
      return this.activityCache
    } catch (error) {
      logging.logError('Failed to load activities from database:', error)
      throw new Error('Failed to load physical activity data')
    }
  }

  // Check if cache needs refresh
  isCacheExpired() {
    if (!this.lastCacheUpdate) return true

    const now = new Date()
    const diffMinutes = (now - this.lastCacheUpdate) / (1000 * 60)
    return diffMinutes > this.cacheExpiryMinutes
  }

  // Get activities with cache management
  async getActivities() {
    if (!this.activityCache || this.isCacheExpired()) {
      await this.loadActivitiesFromDatabase()
    }
    return this.activityCache
  }

  // Get MET value for specific activity, intensity, and activity type
  async getMetValue(activityName, intensity, activityType) {
    try {
      const activities = await this.getActivities()

      // Find matching activity (case-insensitive)
      const activity = activities.find(
        (a) =>
          a['Activity Types'] &&
          a['Activity Types'].toLowerCase() === activityType.toLowerCase() &&
          a['Activity'] &&
          a['Activity'].toLowerCase() === activityName.toLowerCase(),
      )

      if (!activity) {
        logging.logInfo(`Activity not found: ${activityName} (${activityType})`)
        return null
      }

      // Get MET value based on intensity
      let metValue = null
      switch (intensity.toLowerCase()) {
        case 'mild':
          metValue = activity['MET (Mild)']
          break
        case 'moderate':
          metValue = activity['MET (Moderate)']
          break
        case 'intense':
        case 'severe':
          metValue = activity['MET (Severe)']
          break
        default:
          logging.logInfo(`Invalid intensity: ${intensity}`)
          return null
      }

      return parseFloat(metValue) || null
    } catch (error) {
      logging.logError(`Failed to get MET value for ${activityName}:`, error)
      return null
    }
  }

  // Get all activity types
  async getActivityTypes() {
    try {
      const activities = await this.getActivities()
      const types = [
        ...new Set(activities.map((a) => a['Activity Types']).filter(Boolean)),
      ]
      return types.sort()
    } catch (error) {
      logging.logError('Failed to get activity types:', error)
      return []
    }
  }

  // Get activities by type
  async getActivitiesByType(activityType) {
    try {
      const activities = await this.getActivities()
      return activities.filter(
        (a) =>
          a['Activity Types'] &&
          a['Activity Types'].toLowerCase() === activityType.toLowerCase(),
      )
    } catch (error) {
      logging.logError(
        `Failed to get activities by type ${activityType}:`,
        error,
      )
      return []
    }
  }

  // Search activities by name
  async searchActivities(searchTerm) {
    try {
      const activities = await this.getActivities()
      const term = searchTerm.toLowerCase()

      return activities.filter(
        (a) =>
          (a['Activity'] && a['Activity'].toLowerCase().includes(term)) ||
          (a['Activity Types'] &&
            a['Activity Types'].toLowerCase().includes(term)),
      )
    } catch (error) {
      logging.logError(
        `Failed to search activities with term ${searchTerm}:`,
        error,
      )
      return []
    }
  }

  // Get activities with all intensity MET values
  async getActivitiesWithIntensities() {
    try {
      const activities = await this.getActivities()

      return activities.map((activity) => ({
        activityType: activity['Activity Types'],
        activity: activity['Activity'],
        metValues: {
          mild: parseFloat(activity['MET (Mild)']) || 0,
          moderate: parseFloat(activity['MET (Moderate)']) || 0,
          intense: parseFloat(activity['MET (Severe)']) || 0,
        },
      }))
    } catch (error) {
      logging.logError('Failed to get activities with intensities:', error)
      return []
    }
  }

  // Clear cache (useful for testing or manual refresh)
  clearCache() {
    this.activityCache = null
    this.lastCacheUpdate = null
    logging.logInfo('Physical activity cache cleared')
  }
}

module.exports = new PhysicalActivityLookupService()
