const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const physicalActivityLookupService = require('../services/physical-activity-lookup-service')
const DateFilterUtils = require('../utils/date-filter-utils')
const { DashboardFilter } = require('../common/constant')

const patientLifeStyleContainer = 'PatientLifeStyles'

class PatientLifestylePhysicalActivityRepository {
  // Get physical activity data from PatientLifeStyles container with date filtering
  async getPhysicalActivityData(patientId, dateFilter, customDateRange = null) {
    try {
      let query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.source = 'physical_activity_practice_exercise_patterns'`

      // Apply date filtering using the utility
      if (dateFilter && dateFilter !== DashboardFilter.ALL) {
        const dateRange = DateFilterUtils.getDateRange(
          dateFilter,
          customDateRange,
        )
        if (dateRange.startDate && dateRange.endDate) {
          query += ` AND c.created_on >= '${dateRange.startDate}T00:00:00.000Z' AND c.created_on <= '${dateRange.endDate}T23:59:59.999Z'`
        }
      }

      query += ` ORDER BY c.created_on ASC`

      const results = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )
      return results || []
    } catch (error) {
      logging.logError(
        `Failed to get physical activity data for patient ${patientId}:`,
        error,
      )
      throw new Error('Failed to fetch physical activity data')
    }
  }

  // Extract and process physical activity records from lifestyle data
  async processPhysicalActivityRecords(lifestyleRecords, patientWeight) {
    const processedRecords = []

    for (const record of lifestyleRecords) {
      if (!record.questions || !Array.isArray(record.questions)) continue

      for (const question of record.questions) {
        if (!question.fields) continue

        // Handle different field structures
        let activities = []

        if (Array.isArray(question.fields)) {
          // Handle array of arrays structure
          for (const fieldGroup of question.fields) {
            if (Array.isArray(fieldGroup)) {
              activities = activities.concat(fieldGroup)
            } else if (fieldGroup && typeof fieldGroup === 'object') {
              // Handle table structure with numbered keys
              const tableData = Object.keys(fieldGroup)
                .filter((key) => !isNaN(key))
                .map((key) => fieldGroup[key])
                .filter(
                  (item) =>
                    item &&
                    item.activity_type &&
                    item.activity &&
                    item.duration,
                )

              activities = activities.concat(tableData)
            }
          }
        }

        // Process each activity
        for (const activity of activities) {
          if (
            !activity.activity_type ||
            !activity.activity ||
            !activity.duration ||
            !activity.intensity
          ) {
            continue
          }

          // Skip empty activities
          if (
            !activity.activity.trim() ||
            activity.duration === '0' ||
            activity.duration === ''
          ) {
            continue
          }

          const duration = parseFloat(activity.duration)
          if (isNaN(duration) || duration <= 0) continue

          // Get MET value from lookup service
          const metValue = await physicalActivityLookupService.getMetValue(
            activity.activity,
            activity.intensity,
            activity.activity_type,
          )

          // Calculate MET Minutes and Calories
          const metMinutes = metValue ? duration * metValue : 0
          const caloriesBurned = metValue
            ? metValue * patientWeight * (duration / 60)
            : 0

          processedRecords.push({
            id: record.id,
            patientId: record.patientId,
            date: record.created_on.split('T')[0], // Extract date part
            activityType: activity.activity_type,
            activity: activity.activity,
            intensity: activity.intensity,
            duration: duration,
            frequency: activity.frequency || '',
            metValue: metValue || 0,
            metMinutes: metMinutes,
            caloriesBurned: Math.round(caloriesBurned * 100) / 100, // Round to 2 decimal places
            doctorName: record.doctorName || record.doctor?.name || '',
            doctorDesignation:
              record.doctorDesignation || record.doctor?.designation || '',
            created_on: record.created_on,
            updated_on: record.updated_on,
          })
        }
      }
    }

    return processedRecords
  }

  // Get processed physical activity data with calculations
  async getProcessedPhysicalActivityData(
    patientId,
    dateFilter,
    customDateRange = null,
    patientWeight,
  ) {
    try {
      const lifestyleRecords = await this.getPhysicalActivityData(
        patientId,
        dateFilter,
        customDateRange,
      )
      const processedRecords = await this.processPhysicalActivityRecords(
        lifestyleRecords,
        patientWeight,
      )

      return processedRecords
    } catch (error) {
      logging.logError(
        `Failed to get processed physical activity data for patient ${patientId}:`,
        error,
      )
      throw new Error('Failed to process physical activity data')
    }
  }
}

module.exports = new PatientLifestylePhysicalActivityRepository()
