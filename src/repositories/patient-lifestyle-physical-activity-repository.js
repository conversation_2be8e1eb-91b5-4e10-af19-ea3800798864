const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const physicalActivityLookupService = require('../services/physical-activity-lookup-service')
const DateFilterUtils = require('../utils/date-filter-utils')
const { DashboardFilter } = require('../common/constant')
const { result } = require('lodash')

const patientLifeStyleContainer = 'PatientLifeStyles'

class PatientLifestylePhysicalActivityRepository {
  // Get physical activity data from PatientLifeStyles container with date filtering
  async getPhysicalActivityData(patientId, dateFilter, customDateRange = null) {
    try {
      let query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.source = 'physical_activity_practice_exercise_patterns'`

      // Apply date filtering using the utility
      if (dateFilter && dateFilter !== DashboardFilter.ALL) {
        const dateRange = DateFilterUtils.getDateRange(
          dateFilter,
          customDateRange,
        )
        if (dateRange.startDate && dateRange.endDate) {
          query += ` AND c.created_on >= '${dateRange.startDate}T00:00:00.000Z' AND c.created_on <= '${dateRange.endDate}T23:59:59.999Z'`
        }
      }

      query += ` ORDER BY c.created_on ASC`

      const results = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )
      return results || []
    } catch (error) {
      logging.logError(
        `Failed to get physical activity data for patient ${patientId}:`,
        error,
      )
      throw new Error('Failed to fetch physical activity data')
    }
  }

  // Get the latest lifestyle record (by updated_on date)
  async getLatestLifestyleRecord(patientId) {
    try {
      const query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.source = 'physical_activity_practice_exercise_patterns' ORDER BY c.updated_on DESC`

      const results = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )
      console.log(result)

      return results && results.length > 0 ? results[0] : null
    } catch (error) {
      logging.logError(
        `Failed to get latest lifestyle record for patient ${patientId}:`,
        error,
      )
      return null
    }
  }

  // Extract and process physical activity records from lifestyle data
  async processPhysicalActivityRecords(lifestyleRecords, patientWeight) {
    const processedRecords = []

    for (const record of lifestyleRecords) {
      if (!record.questions || !Array.isArray(record.questions)) continue

      for (const question of record.questions) {
        if (!question.fields) continue

        // Handle different field structures
        let activities = []

        if (Array.isArray(question.fields)) {
          // Handle array of arrays structure
          for (const fieldGroup of question.fields) {
            if (Array.isArray(fieldGroup)) {
              activities = activities.concat(fieldGroup)
            } else if (fieldGroup && typeof fieldGroup === 'object') {
              // Handle table structure with numbered keys
              const tableData = Object.keys(fieldGroup)
                .filter((key) => !isNaN(key))
                .map((key) => fieldGroup[key])
                .filter(
                  (item) =>
                    item &&
                    item.activity_type &&
                    item.activity &&
                    item.duration,
                )

              activities = activities.concat(tableData)
            }
          }
        }

        // Process each activity
        for (const activity of activities) {
          if (
            !activity.activity_type ||
            !activity.activity ||
            !activity.duration ||
            !activity.intensity
          ) {
            continue
          }

          // Skip empty activities
          if (
            !activity.activity.trim() ||
            activity.duration === '0' ||
            activity.duration === ''
          ) {
            continue
          }

          const duration = parseFloat(activity.duration)
          if (isNaN(duration) || duration <= 0) continue

          // Get MET value from lookup service
          const metValue = await physicalActivityLookupService.getMetValue(
            activity.activity,
            activity.intensity,
            activity.activity_type,
          )

          // Calculate MET Minutes and Calories
          const metMinutes = metValue ? duration * metValue : 0
          const caloriesBurned = metValue
            ? metValue * patientWeight * (duration / 60)
            : 0

          processedRecords.push({
            id: record.id,
            patientId: record.patientId,
            date: record.created_on.split('T')[0], // Extract date part
            activityType: activity.activity_type,
            activity: activity.activity,
            intensity: activity.intensity,
            duration: duration,
            frequency: activity.frequency || '',
            metValue: metValue || 0,
            metMinutes: metMinutes,
            caloriesBurned: Math.round(caloriesBurned * 100) / 100, // Round to 2 decimal places
            doctorName: record.doctorName || record.doctor?.name || '',
            doctorDesignation:
              record.doctorDesignation || record.doctor?.designation || '',
            created_on: record.created_on,
            updated_on: record.updated_on,
          })
        }
      }
    }

    return processedRecords
  }

  // Generate frequency-based activity projections
  async generateFrequencyBasedActivities(
    latestRecord,
    startDate,
    endDate,
    patientWeight,
  ) {
    const projectedActivities = []

    if (!latestRecord || !latestRecord.questions) {
      return projectedActivities
    }

    // Extract activities from the latest record
    const activities = this.extractActivitiesFromRecord(latestRecord)

    // Generate date range from start to end
    const dateRange = this.generateDateRange(startDate, endDate)

    for (const activity of activities) {
      if (!activity.frequency) continue

      const applicableDates = this.getApplicableDatesForFrequency(
        activity.frequency,
        dateRange,
      )

      for (const date of applicableDates) {
        // Get MET value from lookup service
        const metValue = await physicalActivityLookupService.getMetValue(
          activity.activity,
          activity.intensity,
          activity.activity_type,
        )

        // Calculate MET Minutes and Calories
        const duration = parseFloat(activity.duration)
        const metMinutes = metValue ? duration * metValue : 0
        const caloriesBurned = metValue
          ? metValue * patientWeight * (duration / 60)
          : 0

        // Add day name for better display
        const dayName = new Date(date).toLocaleDateString('en-US', {
          weekday: 'short',
        })

        projectedActivities.push({
          id: `${latestRecord.id}_${activity.activity_type}_${date}`,
          patientId: latestRecord.patientId,
          date: date,
          dayName: dayName,
          activityType: activity.activity_type,
          activity: activity.activity,
          intensity: activity.intensity,
          duration: duration,
          frequency: activity.frequency,
          metValue: metValue || 0,
          metMinutes: metMinutes,
          caloriesBurned: Math.round(caloriesBurned * 100) / 100,
          doctorName:
            latestRecord.doctorName || latestRecord.doctor?.name || '',
          doctorDesignation:
            latestRecord.doctorDesignation ||
            latestRecord.doctor?.designation ||
            '',
          created_on: latestRecord.created_on,
          updated_on: latestRecord.updated_on,
          isProjected: true, // Flag to indicate this is a projected activity
        })
      }
    }

    return projectedActivities
  }

  // Extract activities from a lifestyle record
  extractActivitiesFromRecord(record) {
    const activities = []

    if (!record.questions || !Array.isArray(record.questions)) {
      return activities
    }

    for (const question of record.questions) {
      if (!question.fields) continue

      // Handle different field structures
      if (Array.isArray(question.fields)) {
        for (const fieldGroup of question.fields) {
          if (Array.isArray(fieldGroup)) {
            activities.push(...fieldGroup.filter(this.isValidActivity))
          } else if (fieldGroup && typeof fieldGroup === 'object') {
            // Handle table structure with numbered keys
            const tableData = Object.keys(fieldGroup)
              .filter((key) => !isNaN(key))
              .map((key) => fieldGroup[key])
              .filter(this.isValidActivity)
            activities.push(...tableData)
          }
        }
      }
    }

    return activities
  }

  // Check if activity is valid
  isValidActivity(activity) {
    return (
      activity &&
      activity.activity_type &&
      activity.activity &&
      activity.duration &&
      activity.intensity &&
      activity.activity.trim() &&
      activity.duration !== '0' &&
      activity.duration !== '' &&
      !isNaN(parseFloat(activity.duration)) &&
      parseFloat(activity.duration) > 0
    )
  }

  // Generate date range array
  generateDateRange(startDate, endDate) {
    const dates = []
    const start = new Date(startDate)
    const end = new Date(endDate)

    for (
      let date = new Date(start);
      date <= end;
      date.setDate(date.getDate() + 1)
    ) {
      dates.push(date.toISOString().split('T')[0])
    }

    return dates
  }

  // Get applicable dates based on frequency
  getApplicableDatesForFrequency(frequency, dateRange) {
    switch (frequency.toLowerCase()) {
      case 'daily':
        return dateRange // All dates

      case 'three times a week':
        return dateRange.filter((date) => {
          const dayOfWeek = new Date(date).getDay() // 0 = Sunday, 1 = Monday, etc.
          return dayOfWeek === 1 || dayOfWeek === 2 || dayOfWeek === 3 // Mon, Tue, Wed
        })

      case 'four times a week':
        return dateRange.filter((date) => {
          const dayOfWeek = new Date(date).getDay()
          return (
            dayOfWeek === 1 ||
            dayOfWeek === 2 ||
            dayOfWeek === 3 ||
            dayOfWeek === 4
          ) // Mon, Tue, Wed, Thu
        })

      case 'five times a week':
        return dateRange.filter((date) => {
          const dayOfWeek = new Date(date).getDay()
          return dayOfWeek >= 1 && dayOfWeek <= 5 // Mon to Fri
        })

      case 'six times a week':
        return dateRange.filter((date) => {
          const dayOfWeek = new Date(date).getDay()
          return dayOfWeek !== 0 // All days except Sunday
        })

      case 'weekly':
        return dateRange.filter((date) => {
          const dayOfWeek = new Date(date).getDay()
          return dayOfWeek === 1 // Only Mondays
        })

      default:
        return [] // Unknown frequency
    }
  }

  // Get all lifestyle records within date range (for complex scenarios)
  async getLifestyleRecordsInRange(patientId, startDate, endDate) {
    try {
      const query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.source = 'physical_activity_practice_exercise_patterns' AND c.updated_on >= '${startDate}T00:00:00.000Z' AND c.updated_on <= '${endDate}T23:59:59.999Z' ORDER BY c.updated_on DESC`

      const results = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )

      return results || []
    } catch (error) {
      logging.logError(
        `Failed to get lifestyle records in range for patient ${patientId}:`,
        error,
      )
      return []
    }
  }

  // Get processed physical activity data with calculations and frequency projections
  async getProcessedPhysicalActivityData(
    patientId,
    dateFilter,
    customDateRange = null,
    patientWeight,
  ) {
    try {
      // Determine the date range for projections
      let startDate, endDate

      if (dateFilter && dateFilter !== DashboardFilter.ALL) {
        const dateRange = DateFilterUtils.getDateRange(
          dateFilter,
          customDateRange,
        )
        startDate = dateRange.startDate
        endDate = dateRange.endDate
      } else {
        // If no filter, use last 30 days
        endDate = new Date().toISOString().split('T')[0]
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split('T')[0]
      }

      // Get the latest overall record
      const latestRecord = await this.getLatestLifestyleRecord(patientId)

      if (!latestRecord) {
        logging.logInfo(`No lifestyle records found for patient ${patientId}`)
        return []
      }

      // For now, use simplified logic: project from the latest record's updated date
      // within the requested date range
      const recordStartDate = latestRecord.updated_on.split('T')[0]

      // Project from the later of: record date or requested start date
      const projectionStartDate =
        recordStartDate > startDate ? recordStartDate : startDate

      // Project until the requested end date
      const projectionEndDate = endDate

      logging.logInfo(
        `Projecting activities from ${projectionStartDate} to ${projectionEndDate} using record from ${recordStartDate}`,
      )

      const uniqueActivities = await this.generateFrequencyBasedActivities(
        latestRecord,
        projectionStartDate,
        projectionEndDate,
        patientWeight,
      )

      logging.logInfo(
        `Generated ${uniqueActivities.length} projected activities for patient ${patientId} from ${startDate} to ${endDate}`,
      )

      return uniqueActivities
    } catch (error) {
      logging.logError(
        `Failed to get processed physical activity data for patient ${patientId}:`,
        error,
      )
      throw new Error('Failed to process physical activity data')
    }
  }

  // Helper to get the next record date for range calculation
  getNextRecordDate(currentRecord, allRecords) {
    const currentDate = new Date(currentRecord.updated_on)
    const nextRecord = allRecords
      .filter((r) => new Date(r.updated_on) > currentDate)
      .sort((a, b) => new Date(a.updated_on) - new Date(b.updated_on))[0]

    return nextRecord ? nextRecord.updated_on.split('T')[0] : null
  }

  // Remove duplicate activities (same date, activity type, activity)
  removeDuplicateActivities(activities) {
    const seen = new Set()
    return activities.filter((activity) => {
      const key = `${activity.date}_${activity.activityType}_${activity.activity}`
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }
}

module.exports = new PatientLifestylePhysicalActivityRepository()
